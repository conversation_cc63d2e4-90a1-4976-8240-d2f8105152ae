"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  Toolt<PERSON> as Re<PERSON><PERSON><PERSON><PERSON>tip,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import {
  CHART_COLORS,
  formatCurrency as formatCurrencyUtil,
  formatDate as formatDateUtil,
  formatPercent as formatPercentUtil,
} from '@/lib/chart-utils';
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { DollarSign, HelpCircle, Percent } from "lucide-react";
import {
  GroupedKpiResponse,
  SimpleKpiResponse,
  fetchKpiData,
} from '@/lib/api/dashboard-client';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { useEffect, useRef, useState } from "react";

import { Button } from "./ui/button";
import { Skeleton } from "./ui/skeleton";
import { useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context';
import { useFilters } from '@/lib/contexts/filter-context';

type DisplayMode = "value" | "percent";

type KpiPair = {
  id: string;
  name: string;
  valueKey: string;
  percentKey: string | null;
  hasPercentage: boolean;
  definition: string;
};

// We need to separate the components based on context to avoid conditional hook calls

// Internal component that uses the dashboard context
function DashboardKpiCards() {
  const dashboardContext = useFilters();
  return <KpiCardsContent filters={dashboardContext.filters} getQueryParams={dashboardContext.getQueryParams} />;
}

// Internal component that uses the brand deep dive context
function BrandDeepDiveKpiCardsContent() {
  const brandDeepDiveContext = useBrandDeepDive();
  // const { isStateInitialized } = brandDeepDiveContext; // This is now unused here

  // Convert brand deep dive context to the filter format
  const filters = {
    startDate: brandDeepDiveContext.state.startDate,
    endDate: brandDeepDiveContext.state.endDate,
    currency: brandDeepDiveContext.state.currency,
    groupBy: brandDeepDiveContext.state.groupBy,
    brands: brandDeepDiveContext.state.selectedBrand ? [brandDeepDiveContext.state.selectedBrand] : [],
    brandGroups: [],
    salesChannels: brandDeepDiveContext.state.salesChannels,
    countryNames: brandDeepDiveContext.state.countryNames,
  };

  // Log to ensure the selected brand is being used
  console.log('Brand Deep Dive KPI Cards - Selected Brand:', brandDeepDiveContext.state.selectedBrand);

  // Use the getQueryParams function directly from the context rather than recreating it
  const { getQueryParams, isStateInitialized: brandDiveIsStateInitialized } = brandDeepDiveContext; // Renamed for clarity

  return <KpiCardsContent filters={filters} getQueryParams={getQueryParams} isStateInitialized={brandDiveIsStateInitialized} />;
}

// Add contextType prop to specify which context to use
export function EcommerceKpiCards({ contextType = 'dashboard' }: { contextType?: 'dashboard' | 'brandDeepDive' }) {
  // Render the appropriate component based on the context type
  if (contextType === 'brandDeepDive') {
    try {
      return <BrandDeepDiveKpiCardsContent />;
    } catch (error) {
      console.warn('Brand deep dive context not available, falling back to dashboard context:', error);
      return <DashboardKpiCards />;
    }
  }

  return <DashboardKpiCards />;
}

// Define proper types for filters
type FilterState = {
  startDate: string;
  endDate: string;
  currency: string;
  groupBy: string;
  brands: string[];
  brandGroups: string[];
  salesChannels: string[];
  countryNames: string[];
};

// Shared component that takes filters and renders KPI cards
function KpiCardsContent({
  filters,
  getQueryParams,
  isStateInitialized // Add the new prop here
}: {
  filters: FilterState;
  getQueryParams: () => string;
  isStateInitialized?: boolean; // Make it optional for DashboardKpiCards
}) {
  const [kpiData, setKpiData] = useState<SimpleKpiResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [displayModes, setDisplayModes] = useState<Record<string, DisplayMode>>({});
  const [initialLoadDelay, setInitialLoadDelay] = useState(true);

  // Add AbortController ref to manage in-flight requests
  const abortControllerRef = useRef<AbortController | null>(null);

  // Add a small delay on initial load to prevent immediate "all brands" query
  useEffect(() => {
    const timer = setTimeout(() => {
      setInitialLoadDelay(false);
    }, 1000); // 1 second delay to allow filter context to initialize

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    async function fetchData() {
      console.log('[EcommerceKpiCards] fetchData called with filters:', {
        brands: filters.brands,
        brandsCount: filters.brands.length,
        isStateInitialized,
        initialLoadDelay,
        startDate: filters.startDate,
        endDate: filters.endDate
      });

      // Only fetch if the state has been initialized (or if not provided, assume true for dashboard context)
      if (isStateInitialized === false) { // Explicitly check for false
        console.log('[EcommerceKpiCards] State not initialized, skipping fetch');
        setLoading(false); // Not loading if we're not ready to fetch
        return;
      }

      // Add delay on initial load to prevent immediate "all brands" query
      if (initialLoadDelay) {
        console.log('[EcommerceKpiCards] Initial load delay active, skipping fetch');
        setLoading(false);
        return;
      }

      // Abort any in-flight requests
      if (abortControllerRef.current) {
        console.log('[EcommerceKpiCards] Aborting previous request');
        abortControllerRef.current.abort();
      }

      // Create a new AbortController for this request
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      try {
        setLoading(true);
        setError(null);

        // Create the parameters for the API request directly from the filters
        const params: Record<string, string | string[]> = {
          startDate: filters.startDate,
          endDate: filters.endDate,
          currency: filters.currency,
          groupByTime: filters.groupBy,
          // Include all filter parameters
          brands: filters.brands.length > 0 ? filters.brands : [],
          salesChannels: filters.salesChannels.length > 0 ? filters.salesChannels : [],
          countryNames: filters.countryNames.length > 0 ? filters.countryNames : [],
          brandGroups: filters.brandGroups.length > 0 ? filters.brandGroups : [],
          // Always ensure we request the KPIs we need
          kpis: [
            "Gross Revenue",
            "Net Revenue",
            "Discount",
            "% Discount",
            "Refund",
            "% Refund",
            "Landed Cost",
            "% Landed Cost",
            "Fulfillment Cost",
            "% Fulfillment Cost",
            "Transaction Cost",
            "% Transaction Cost",
            "Gross Margin",
            "% Gross Margin",
            "Adspend",
            "% Adspend",
            "Contribution Margin",
            "% Contribution Margin",
          ]
        };

        console.log('[EcommerceKpiCards] Making API request with params:', {
          brands: params.brands,
          brandsIsEmpty: Array.isArray(params.brands) && params.brands.length === 0,
          kpisCount: Array.isArray(params.kpis) ? params.kpis.length : 0
        });

        const data = await fetchKpiData(params, signal);
        console.log('[EcommerceKpiCards] API request completed successfully');

        if (
          "Gross Revenue" in data &&
          typeof data["Gross Revenue"] === "object" &&
          !("summary" in data["Gross Revenue"])
        ) {
          console.warn(
            "Received grouped response, but this component only supports simple responses"
          );
          const firstDim = Object.keys(data["Gross Revenue"])[0];
          if (firstDim) {
            const simple: SimpleKpiResponse = {};
            Object.keys(data).forEach((k) => {
              simple[k] = (data as GroupedKpiResponse)[k][firstDim];
            });
            setKpiData(simple);
          } else {
            throw new Error("No dimension data available");
          }
        } else {
          setKpiData(data as SimpleKpiResponse);
        }
      } catch (err) {
        // Don't show errors for aborted requests
        if ((err as Error).name !== 'AbortError') {
          console.error("Failed to load KPI data:", err);

          // Provide more specific error messages based on error type
          let errorMessage = "Failed to load KPI data";
          if (err instanceof Error) {
            if (err.message.includes('timeout')) {
              errorMessage = "Request timed out. The dashboard is loading a large dataset. Please try selecting specific brands or refresh the page.";
            } else if (err.message.includes('500')) {
              errorMessage = "Server error occurred. Please refresh the page or try again in a moment.";
            } else if (err.message.includes('Failed to fetch')) {
              errorMessage = "Network error. Please check your connection and try again.";
            }
          }

          setError(errorMessage);
        }
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [filters, getQueryParams, isStateInitialized, initialLoadDelay]); // Add both isStateInitialized and initialLoadDelay to dependency array

  // formatting helpers
  const formatCurrency = (v: number | null) =>
    formatCurrencyUtil(v, filters.currency);
  const formatPercent = formatPercentUtil;
  // formatDate is directly used via formatDateUtil

  // Recharts formatters need specific signatures
  const rechartsDateFormatter = (value: string) => formatDateUtil(value);
  const rechartsLabelFormatter = (label: string) => formatDateUtil(label);

  // define your KPIs
  const kpiPairs: KpiPair[] = [
    {
      id: "revenue",
      name: "Revenue",
      valueKey: "Gross Revenue",
      percentKey: null,
      hasPercentage: false,
      definition:
        "Total sales revenue before any deductions (discounts, refunds, taxes, shipping, etc.).",
    },
    {
      id: "discount",
      name: "Discount",
      valueKey: "Discount",
      percentKey: "% Discount",
      hasPercentage: true,
      definition:
        "Total value of promotional discounts or coupons applied to orders.",
    },
    {
      id: "refund",
      name: "Refunds",
      valueKey: "Refund",
      percentKey: "% Refund",
      hasPercentage: true,
      definition:
        "Total value of refunded transactions (including full and partial refunds).",
    },
    {
      id: "netRevenue",
      name: "Net Revenue",
      valueKey: "Net Revenue",
      percentKey: null,
      hasPercentage: false,
      definition:
        "Revenue after accounting for discounts, refunds, and excluding taxes.",
    },
    {
      id: "landedCost",
      name: "Landed Cost",
      valueKey: "Landed Cost",
      percentKey: "% Landed Cost",
      hasPercentage: true,
      definition:
        "Total cost to acquire a product, including manufacturing, freight, duties, and handling.",
    },
    {
      id: "fulfillmentCost",
      name: "Fulfillment Cost",
      valueKey: "Fulfillment Cost",
      percentKey: "% Fulfillment Cost",
      hasPercentage: true,
      definition:
        "Costs related to storing, picking, packing, and shipping the products (e.g., 3PL or FBA fees).",
    },
    {
      id: "transactionCost",
      name: "Transaction Cost",
      valueKey: "Transaction Cost",
      percentKey: "% Transaction Cost",
      hasPercentage: true,
      definition:
        "Payment processing and marketplace transaction costs (e.g., Stripe, Shopify, Amazon referral fees).",
    },
    {
      id: "grossMargin",
      name: "Gross Margin",
      valueKey: "Gross Margin",
      percentKey: "% Gross Margin",
      hasPercentage: true,
      definition:
        "Revenue retained after accounting for product costs (before fulfillment, ads, etc.).",
    },
    {
      id: "adspend",
      name: "Adspend",
      valueKey: "Adspend",
      percentKey: "% Adspend",
      hasPercentage: true,
      definition:
        "Total advertising spend across all channels (Meta, Google, Amazon, etc.).",
    },
    {
      id: "contributionMargin",
      name: "Contribution Margin",
      valueKey: "Contribution Margin",
      percentKey: "% Contribution Margin",
      hasPercentage: true,
      definition:
        "Profitability after accounting for product costs and advertising spend. Calculated as Gross Margin - Adspend.",
    },
  ];

  // initialize display modes
  useEffect(() => {
    const modes: Record<string, DisplayMode> = {};
    kpiPairs.forEach((k) => {
      modes[k.id] = "value";
    });
    setDisplayModes(modes);
  }, []);

  // build chart data by date
  const processChartData = () => {
    if (!kpiData) return [];
    const firstKey = Object.keys(kpiData)[0];
    if (!firstKey || !kpiData[firstKey]?.timeSeries) return [];

    const dateMap: Record<string, Record<string, string | number | null>> = {};
    kpiData[firstKey].timeSeries.forEach((p) => {
      dateMap[p.date] = { date: p.date };
    });
    Object.entries(kpiData).forEach(([name, data]) => {
      data.timeSeries.forEach((p) => {
        dateMap[p.date][name] = p.value;
      });
    });
    return Object.values(dateMap).sort(
      (a, b) =>
        new Date(a.date as string).getTime() - new Date(b.date as string).getTime()
    );
  };
  const chartData = processChartData();

  const customTooltipFormatter = (value: unknown, name: string) => {
    if (typeof value !== "number") return "N/A";
    return name.startsWith("% ")
      ? formatPercent(value)
      : formatCurrency(value);
  };

  // Helper function to get the calculation formula for percentage KPIs
  const getCalculationFormula = (kpi: KpiPair): string | null => {
    if (!kpi.hasPercentage || !kpi.percentKey) return null;

    // Determine the denominator based on the KPI
    const isDenominatorGrossRevenue = kpi.id === "discount" || kpi.id === "refund";
    const denominator = isDenominatorGrossRevenue ? "Gross Revenue" : "Net Revenue";

    return `${kpi.valueKey} / ${denominator}`;
  };

  // Helper function to get the actual values for the calculation display
  const getCalculationValues = (kpi: KpiPair): { numerator: number | null; denominator: number | null } | null => {
    if (!kpi.hasPercentage || !kpi.percentKey || !kpiData) return null;

    const numerator = kpiData[kpi.valueKey]?.summary.value ?? null;
    const isDenominatorGrossRevenue = kpi.id === "discount" || kpi.id === "refund";
    const denominatorKey = isDenominatorGrossRevenue ? "Gross Revenue" : "Net Revenue";
    const denominator = kpiData[denominatorKey]?.summary.value ?? null;

    return { numerator, denominator };
  };

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
        <Card className="col-span-full">
          <CardContent className="p-6">
            <div className="text-center text-red-500">{error}</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="px-3 sm:px-4 lg:px-6">
      <div className="mb-3 sm:mb-4">
        <h3 className="text-base sm:text-lg font-medium">Key Performance Indicators</h3>
      </div>

      <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
        {kpiPairs.map((kpi) => (
          <Card key={kpi.id}>
            <CardHeader className="flex items-center justify-between pb-2">
              <div className="flex items-center space-x-1">
                <CardTitle className="text-sm font-medium">
                  {kpi.name}
                </CardTitle>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 p-0"
                      >
                        <HelpCircle className="h-3 w-3" />
                        <span className="sr-only">KPI Definition</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">{kpi.definition}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              {kpi.hasPercentage && (
                <div className="flex items-center space-x-1">
                  <Button
                    variant={
                      displayModes[kpi.id] === "value" ? "default" : "outline"
                    }
                    size="icon"
                    className="h-6 w-6"
                    onClick={() =>
                      setDisplayModes((p) => ({ ...p, [kpi.id]: "value" }))
                    }
                    title="Show Values"
                  >
                    <DollarSign className="h-3 w-3" />
                  </Button>
                  <Button
                    variant={
                      displayModes[kpi.id] === "percent"
                        ? "default"
                        : "outline"
                    }
                    size="icon"
                    className="h-6 w-6"
                    onClick={() =>
                      setDisplayModes((p) => ({
                        ...p,
                        [kpi.id]: "percent",
                      }))
                    }
                    title="Show Percentages"
                  >
                    <Percent className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-2">
                {loading ? (
                  <Skeleton className="h-8 w-[120px]" />
                ) : kpi.hasPercentage && displayModes[kpi.id] === "percent" && kpi.percentKey ? (
                  <div className="flex items-center justify-between">
                    <span>{formatPercent(kpiData?.[kpi.percentKey]?.summary.value ?? null)}</span>
                    {(() => {
                      const calculationValues = getCalculationValues(kpi);
                      const formula = getCalculationFormula(kpi);
                      if (calculationValues && formula) {
                        return (
                          <span className="text-xs text-muted-foreground ml-2 flex-shrink-0">
                            {formatCurrency(calculationValues.numerator)} / {formatCurrency(calculationValues.denominator)}
                          </span>
                        );
                      }
                      return null;
                    })()}
                  </div>
                ) : (
                  formatCurrency(kpiData?.[kpi.valueKey]?.summary.value ?? null)
                )}
              </div>
              <div className="h-[120px]">
                {loading ? (
                  <Skeleton className="h-full w-full" />
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={chartData}
                      margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                    >
                      <XAxis
                        dataKey="date"
                        tickFormatter={rechartsDateFormatter}
                        minTickGap={30}
                        tick={{ fontSize: 10 }}
                        height={15}
                      />
                      <YAxis
                        tickFormatter={(val) =>
                          displayModes[kpi.id] === "value"
                            ? new Intl.NumberFormat("en-CA", {
                                maximumFractionDigits: 0,
                              }).format(val)
                            : new Intl.NumberFormat("en-CA", {
                                style: "percent",
                                minimumFractionDigits: 1,
                                maximumFractionDigits: 2,
                              }).format(val)
                        }
                        tick={{ fontSize: 10 }}
                        width={50}
                      />
                      <CartesianGrid strokeDasharray="3 3" />
                      <RechartsTooltip
                        formatter={customTooltipFormatter}
                        labelFormatter={rechartsLabelFormatter}
                      />
                      <Bar
                        dataKey={
                          kpi.hasPercentage &&
                          displayModes[kpi.id] === "percent" &&
                          kpi.percentKey
                            ? kpi.percentKey
                            : kpi.valueKey
                        }
                        fill={CHART_COLORS.blue}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
